@echo off
echo Starting AI Voice Agent Platform with Data Integration...
echo.

echo Step 1: Starting core services (PostgreSQL, Redis, Backend, Frontend)...
docker-compose up -d postgres redis backend frontend

echo.
echo Step 2: Waiting for core services to be ready...
timeout /t 10 /nobreak > nul

echo.
echo Step 3: Starting data integration services (Qdrant, MinIO, Data Integration Service)...
docker-compose --profile data-integration up -d

echo.
echo Step 4: Installing frontend dependencies...
cd frontend
npm install
cd ..

echo.
echo ========================================
echo   AI Voice Agent Platform is starting!
echo ========================================
echo.
echo Frontend:              http://localhost:3000
echo Main API:              http://localhost:8000/docs
echo Data Integration API:   http://localhost:8001/docs
echo Vector Database:        http://localhost:6333/dashboard
echo File Storage Console:   http://localhost:9001
echo Database Admin:         http://localhost:8080
echo.
echo To test the Data Integration:
echo 1. Open http://localhost:3000
echo 2. Login with your credentials
echo 3. Navigate to "Data Management"
echo 4. Create a database
echo 5. Upload files (Excel, CSV, JSON, PDF, etc.)
echo 6. Monitor processing status
echo.
echo Press any key to view logs...
pause > nul

echo.
echo Showing service logs (Ctrl+C to exit)...
docker-compose logs -f data-integration
