@echo off
echo Checking AI Voice Agent Platform Services...
echo.

echo ========================================
echo   Service Status Check
echo ========================================
echo.

echo Checking Docker containers...
docker-compose ps

echo.
echo ========================================
echo   Port Status Check
echo ========================================
echo.

echo Checking Frontend (port 3000)...
curl -s http://localhost:3000 > nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Frontend: RUNNING - http://localhost:3000
) else (
    echo ❌ Frontend: NOT ACCESSIBLE
)

echo.
echo Checking Main Backend (port 8000)...
curl -s http://localhost:8000/health > nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Main Backend: RUNNING - http://localhost:8000/docs
) else (
    echo ❌ Main Backend: NOT ACCESSIBLE
)

echo.
echo Checking Data Integration Service (port 8001)...
curl -s http://localhost:8001/health > nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Data Integration: RUNNING - http://localhost:8001/docs
) else (
    echo ❌ Data Integration: NOT ACCESSIBLE
    echo.
    echo To start Data Integration Service:
    echo   .\start-data-integration.bat
)

echo.
echo Checking Vector Database (port 6333)...
curl -s http://localhost:6333/dashboard > nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Vector Database: RUNNING - http://localhost:6333/dashboard
) else (
    echo ❌ Vector Database: NOT ACCESSIBLE
)

echo.
echo Checking File Storage (port 9000)...
curl -s http://localhost:9000/minio/health/live > nul 2>&1
if %errorlevel% == 0 (
    echo ✅ File Storage: RUNNING - http://localhost:9001
) else (
    echo ❌ File Storage: NOT ACCESSIBLE
)

echo.
echo ========================================
echo   Quick Actions
echo ========================================
echo.
echo 1. Start all services:        .\start.bat
echo 2. Start data integration:    .\start-data-integration.bat
echo 3. View logs:                 docker-compose logs -f
echo 4. Stop all services:         docker-compose down
echo.
pause
