version: '3.8'

services:
  # Data Integration Service
  data-integration:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mvp_data_integration
    environment:
      - DATABASE_URL=*********************************************************/voiceagent_db
      - REDIS_URL=redis://redis:6379
      - VECTOR_DB_URL=http://qdrant:6333
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MCP_SERVER_PORT=8001
      - FILE_STORAGE_PATH=/app/storage
      - MAX_FILE_SIZE=100MB
      - SUPPORTED_FORMATS=xlsx,csv,json,pdf,txt,docx
    ports:
      - "8001:8001"  # Main API
      - "8002:8002"  # MCP Server
      - "8003:8003"  # WebSocket for real-time updates
    volumes:
      - ./storage:/app/storage
      - ./config:/app/config
    depends_on:
      - qdrant
      - postgres
      - redis
    networks:
      - mvp_network
    restart: unless-stopped

  # Vector Database for Semantic Search
  qdrant:
    image: qdrant/qdrant:latest
    container_name: mvp_qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    networks:
      - mvp_network
    restart: unless-stopped

  # MinIO for File Storage (S3-compatible)
  minio:
    image: minio/minio:latest
    container_name: mvp_minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - mvp_network
    restart: unless-stopped

  # Elasticsearch for Advanced Search (Optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: mvp_elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - mvp_network
    restart: unless-stopped
    profiles:
      - advanced

volumes:
  qdrant_data:
    driver: local
  minio_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  mvp_network:
    external: true
