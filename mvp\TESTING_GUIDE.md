# 🧪 Data Integration Testing Guide

## 🚀 Quick Start

### 1. **Start All Services**
```bash
# Windows
mvp\start-data-integration.bat

# Linux/Mac
chmod +x mvp/start-data-integration.sh
mvp/start-data-integration.sh
```

### 2. **Access the Application**
- **Frontend**: http://localhost:3000
- **Login**: Use your existing credentials or register a new account

## 📋 Step-by-Step Testing

### **Step 1: Create a Database**
1. Navigate to **"Data Management"** in the sidebar
2. Click **"Create Database"** button
3. Fill in the form:
   - **Name**: `customer_data`
   - **Description**: `Customer information and contact details`
   - **Type**: `Internal (Managed)`
4. Click **"Create Database"**
5. ✅ **Expected**: Database appears in the list with "Active" status

### **Step 2: Upload Test Files**
1. Go to **"File Upload"** tab
2. Select the database you just created
3. Add description: `Sample customer and product data`
4. **Upload the sample CSV**:
   - Drag & drop `mvp/test-data/sample-customers.csv`
   - Or click to select the file
5. Click **"Upload Files"**
6. ✅ **Expected**: Success message and file starts processing

### **Step 3: Monitor Processing**
1. Go to **"Data Sources"** tab
2. Find your uploaded file
3. Click **"View Status"** (eye icon)
4. ✅ **Expected**: 
   - Status changes from "Pending" → "Processing" → "Completed"
   - Progress bar shows completion percentage
   - Records count shows processed data

### **Step 4: Upload JSON File**
1. Return to **"File Upload"** tab
2. Upload `mvp/test-data/sample-products.json`
3. Monitor processing in **"Data Sources"** tab
4. ✅ **Expected**: JSON file processed successfully

### **Step 5: Test Semantic Search**
1. Go to **"Search"** tab
2. Enter a natural language query: `"customer contact information"`
3. Select a database or leave as "All Databases"
4. Click **"Search"**
5. ✅ **Expected**:
   - Relevant results from uploaded files
   - Similarity scores displayed
   - Fast execution time

### **Step 6: Test Agent Binding**
1. Go to **"Agent Bindings"** tab
2. Select a database from dropdown
3. Click **"Bind Agent"**
4. Select an agent and configure permissions
5. Click **"Bind Agent"**
6. ✅ **Expected**: Agent appears in bindings table

### **Step 7: Test MCP Server Dashboard**
1. Go to **"MCP Server"** tab
2. View server statistics and status
3. Monitor active connections
4. ✅ **Expected**:
   - Server status shows "running"
   - Statistics display correctly
   - No connection errors

### **Step 8: Test Multiple File Upload**
1. Create another database: `mixed_data`
2. Upload both CSV and JSON files together
3. ✅ **Expected**: Both files upload and process independently

## 🔍 What to Look For

### **✅ Success Indicators**
- Databases create without errors
- Files upload with progress indication
- Processing status updates in real-time
- No error messages in browser console
- File sizes and types display correctly

### **🚨 Potential Issues**
- **"Failed to load data"**: Data Integration Service not running
- **Upload fails**: Check file size (max 100MB) and format
- **Processing stuck**: Check backend logs for errors
- **No real-time updates**: WebSocket connection issues

## 🛠️ Troubleshooting

### **Issue: Data Integration Service Not Running**
```bash
# Check service status
docker-compose ps

# Start data integration services
docker-compose --profile data-integration up -d

# Check logs
docker-compose logs data-integration
```

### **Issue: Frontend Dependencies Missing**
```bash
cd mvp/frontend
npm install
npm start
```

### **Issue: File Upload Fails**
1. Check file format (must be: xlsx, xls, csv, json, pdf, txt, docx)
2. Check file size (must be < 100MB)
3. Ensure database is selected
4. Check browser network tab for API errors

### **Issue: Processing Stuck**
```bash
# Check data integration service logs
docker-compose logs -f data-integration

# Check if Qdrant is running
curl http://localhost:6333/health

# Check if MinIO is running
curl http://localhost:9000/minio/health/live
```

## 📊 Service Endpoints

### **Health Checks**
- Main Backend: http://localhost:8000/health
- Data Integration: http://localhost:8001/health
- Vector Database: http://localhost:6333/health
- File Storage: http://localhost:9000/minio/health/live

### **API Documentation**
- Main API: http://localhost:8000/docs
- Data Integration API: http://localhost:8001/docs

### **Admin Interfaces**
- Vector Database: http://localhost:6333/dashboard
- File Storage Console: http://localhost:9001 (admin/minioadmin123)
- Database Admin: http://localhost:8080

## 🎯 Expected Results

After successful testing, you should have:

1. **Created Databases**: Visible in the databases tab
2. **Uploaded Files**: Listed in data sources with processing status
3. **Processed Data**: Files showing "Completed" status with record counts
4. **Real-time Updates**: Status changes visible without page refresh

## 🔄 Advanced Testing

### **Test Error Handling**
1. Upload an unsupported file type (.exe, .zip)
2. Upload a file larger than 100MB
3. Try to upload without selecting a database

### **Test Real-time Updates**
1. Upload a large file
2. Keep the status dialog open
3. Watch progress update in real-time

### **Test Multiple Databases**
1. Create multiple databases
2. Upload files to different databases
3. Verify data isolation

## 📝 Test Data

The provided test files contain:

### **sample-customers.csv**
- 10 customer records
- Fields: id, name, email, phone, company, status, created_date
- Tests CSV parsing and tabular data

### **sample-products.json**
- 5 product records
- Nested JSON structure with arrays
- Tests JSON parsing and complex data types

## 🎉 Success Criteria

✅ **Complete Success**: All files upload, process, and show "Completed" status
✅ **Partial Success**: Files upload but some processing errors (check logs)
❌ **Failure**: Cannot create databases or upload files (check service status)

This testing guide ensures the Data Integration feature works end-to-end from the user perspective!
