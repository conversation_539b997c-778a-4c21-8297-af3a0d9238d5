import React from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { Box } from '@mui/material';

interface AnalyticsChartProps {
  data: any[];
  xKey: string;
  yKey: string;
  type: 'line' | 'bar';
  color: string;
  height?: number;
}

const AnalyticsChart: React.FC<AnalyticsChartProps> = ({
  data,
  xKey,
  yKey,
  type,
  color,
  height = 300,
}) => {
  const formatXAxisLabel = (value: string) => {
    // If it looks like a date, format it
    if (value.includes('-')) {
      const date = new Date(value);
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }
    // If it's a long string, truncate it
    if (value.length > 10) {
      return value.substring(0, 10) + '...';
    }
    return value;
  };

  return (
    <Box sx={{ width: '100%', height }}>
      <ResponsiveContainer width="100%" height="100%">
        {type === 'line' ? (
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey={xKey} 
              tickFormatter={formatXAxisLabel}
              angle={-45}
              textAnchor="end"
              height={60}
            />
            <YAxis />
            <Tooltip 
              labelFormatter={(value) => {
                if (typeof value === 'string' && value.includes('-')) {
                  return new Date(value).toLocaleDateString();
                }
                return value;
              }}
            />
            <Line 
              type="monotone" 
              dataKey={yKey} 
              stroke={color} 
              strokeWidth={2}
              dot={{ fill: color, strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        ) : (
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey={xKey} 
              tickFormatter={formatXAxisLabel}
              angle={-45}
              textAnchor="end"
              height={60}
            />
            <YAxis />
            <Tooltip 
              labelFormatter={(value) => {
                if (typeof value === 'string' && value.includes('-')) {
                  return new Date(value).toLocaleDateString();
                }
                return value;
              }}
            />
            <Bar dataKey={yKey} fill={color} />
          </BarChart>
        )}
      </ResponsiveContainer>
    </Box>
  );
};

export default AnalyticsChart;
