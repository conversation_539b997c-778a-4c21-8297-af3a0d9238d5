# Database Configuration
DATABASE_URL=postgresql://voiceagent:voiceagent_password@localhost:5432/voiceagent_db
POSTGRES_DB=voiceagent_db
POSTGRES_USER=voiceagent
POSTGRES_PASSWORD=voiceagent_password

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production-minimum-32-characters
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# API Keys (Demo/Mock values for MVP)
OPENAI_API_KEY=sk-demo-key-for-mvp-replace-with-real-key
ELEVENLABS_API_KEY=demo-elevenlabs-key
PINECONE_API_KEY=demo-pinecone-key
PINECONE_ENVIRONMENT=demo-environment

# Twilio Configuration (Demo values)
TWILIO_ACCOUNT_SID=demo-account-sid
TWILIO_AUTH_TOKEN=demo-auth-token
TWILIO_PHONE_NUMBER=+**********

# Application Configuration
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
API_V1_STR=/api/v1

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# File Storage Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=********  # 10MB in bytes

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Monitoring and Logging
SENTRY_DSN=https://<EMAIL>/project-id
LOG_FILE=./logs/app.log

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000/api/v1
REACT_APP_ENVIRONMENT=development
REACT_APP_APP_NAME=Voice Agent Platform
REACT_APP_VERSION=1.0.0-mvp

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_TIMEOUT_MINUTES=60

# Demo/Mock Configuration
ENABLE_MOCK_SERVICES=true
MOCK_CALL_DURATION_SECONDS=120
MOCK_RESPONSE_DELAY_MS=500

# Stripe Configuration (for future billing integration)
STRIPE_PUBLISHABLE_KEY=pk_test_demo_key
STRIPE_SECRET_KEY=sk_test_demo_key
STRIPE_WEBHOOK_SECRET=whsec_demo_secret

# Analytics Configuration
ENABLE_ANALYTICS=true
ANALYTICS_RETENTION_DAYS=90

# Feature Flags
ENABLE_VOICE_CLONING=false
ENABLE_MULTI_LANGUAGE=false
ENABLE_CRM_INTEGRATION=false
ENABLE_ADVANCED_ANALYTICS=false
